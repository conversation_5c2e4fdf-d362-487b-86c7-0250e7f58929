/* display-utilities.css */

/* Default (Mobile-First) Display Utilities */
.block {
  display: block !important;
}
.inline-block {
  display: inline-block !important;
}
.inline {
  display: inline !important;
}
.flex {
  display: flex !important;
}
.inline-flex {
  display: inline-flex !important;
}
.grid {
  display: grid !important;
}
.inline-grid {
  display: inline-grid !important;
}
.table {
  display: table !important;
}
.table-caption {
  display: table-caption !important;
}
.table-cell {
  display: table-cell !important;
}
.table-column {
  display: table-column !important;
}
.table-column-group {
  display: table-column-group !important;
}
.table-footer-group {
  display: table-footer-group !important;
}
.table-header-group {
  display: table-header-group !important;
}
.table-row-group {
  display: table-row-group !important;
}
.table-row {
  display: table-row !important;
}
.flow-root {
  display: flow-root !important;
}
.contents {
  display: contents !important;
}
.list-item {
  display: list-item !important;
}
.hidden {
  display: none !important;
} /* Common alias for display: none; */

/* Small screens (sm: 640px and up) */
@media (min-width: 640px) {
  .sm\:block {
    display: block !important;
  }
  .sm\:inline-block {
    display: inline-block !important;
  }
  .sm\:inline {
    display: inline !important;
  }
  .sm\:flex {
    display: flex !important;
  }
  .sm\:inline-flex {
    display: inline-flex !important;
  }
  .sm\:grid {
    display: grid !important;
  }
  .sm\:inline-grid {
    display: inline-grid !important;
  }
  .sm\:table {
    display: table !important;
  }
  .sm\:table-caption {
    display: table-caption !important;
  }
  .sm\:table-cell {
    display: table-cell !important;
  }
  .sm\:table-column {
    display: table-column !important;
  }
  .sm\:table-column-group {
    display: table-column-group !important;
  }
  .sm\:table-footer-group {
    display: table-footer-group !important;
  }
  .sm\:table-header-group {
    display: table-header-group !important;
  }
  .sm\:table-row-group {
    display: table-row-group !important;
  }
  .sm\:table-row {
    display: table-row !important;
  }
  .sm\:flow-root {
    display: flow-root !important;
  }
  .sm\:contents {
    display: contents !important;
  }
  .sm\:list-item {
    display: list-item !important;
  }
  .sm\:hidden {
    display: none !important;
  }
}

/* Medium screens (md: 768px and up) */
@media (min-width: 768px) {
  .md\:block {
    display: block !important;
  }
  .md\:inline-block {
    display: inline-block !important;
  }
  .md\:inline {
    display: inline !important;
  }
  .md\:flex {
    display: flex !important;
  }
  .md\:inline-flex {
    display: inline-flex !important;
  }
  .md\:grid {
    display: grid !important;
  }
  .md\:inline-grid {
    display: inline-grid !important;
  }
  .md\:table {
    display: table !important;
  }
  .md\:table-caption {
    display: table-caption !important;
  }
  .md\:table-cell {
    display: table-cell !important;
  }
  .md\:table-column {
    display: table-column !important;
  }
  .md\:table-column-group {
    display: table-column-group !important;
  }
  .md\:table-footer-group {
    display: table-footer-group !important;
  }
  .md\:table-header-group {
    display: table-header-group !important;
  }
  .md\:table-row-group {
    display: table-row-group !important;
  }
  .md\:table-row {
    display: table-row !important;
  }
  .md\:flow-root {
    display: flow-root !important;
  }
  .md\:contents {
    display: contents !important;
  }
  .md\:list-item {
    display: list-item !important;
  }
  .md\:hidden {
    display: none !important;
  }
}

/* Large screens (lg: 1024px and up) */
@media (min-width: 1024px) {
  .lg\:block {
    display: block !important;
  }
  .lg\:inline-block {
    display: inline-block !important;
  }
  .lg\:inline {
    display: inline !important;
  }
  .lg\:flex {
    display: flex !important;
  }
  .lg\:inline-flex {
    display: inline-flex !important;
  }
  .lg\:grid {
    display: grid !important;
  }
  .lg\:inline-grid {
    display: inline-grid !important;
  }
  .lg\:table {
    display: table !important;
  }
  .lg\:table-caption {
    display: table-caption !important;
  }
  .lg\:table-cell {
    display: table-cell !important;
  }
  .lg\:table-column {
    display: table-column !important;
  }
  .lg\:table-column-group {
    display: table-column-group !important;
  }
  .lg\:table-footer-group {
    display: table-footer-group !important;
  }
  .lg\:table-header-group {
    display: table-header-group !important;
  }
  .lg\:table-row-group {
    display: table-row-group !important;
  }
  .lg\:table-row {
    display: table-row !important;
  }
  .lg\:flow-root {
    display: flow-root !important;
  }
  .lg\:contents {
    display: contents !important;
  }
  .lg\:list-item {
    display: list-item !important;
  }
  .lg\:hidden {
    display: none !important;
  }
}

/* Extra-Large screens (xl: 1280px and up) */
@media (min-width: 1280px) {
  .xl\:block {
    display: block !important;
  }
  .xl\:inline-block {
    display: inline-block !important;
  }
  .xl\:inline {
    display: inline !important;
  }
  .xl\:flex {
    display: flex !important;
  }
  .xl\:inline-flex {
    display: inline-flex !important;
  }
  .xl\:grid {
    display: grid !important;
  }
  .xl\:inline-grid {
    display: inline-grid !important;
  }
  .xl\:table {
    display: table !important;
  }
  .xl\:table-caption {
    display: table-caption !important;
  }
  .xl\:table-cell {
    display: table-cell !important;
  }
  .xl\:table-column {
    display: table-column !important;
  }
  .xl\:table-column-group {
    display: table-column-group !important;
  }
  .xl\:table-footer-group {
    display: table-footer-group !important;
  }
  .xl\:table-header-group {
    display: table-header-group !important;
  }
  .xl\:table-row-group {
    display: table-row-group !important;
  }
  .xl\:table-row {
    display: table-row !important;
  }
  .xl\:flow-root {
    display: flow-root !important;
  }
  .xl\:contents {
    display: contents !important;
  }
  .xl\:list-item {
    display: list-item !important;
  }
  .xl\:hidden {
    display: none !important;
  }
}

/* FLEX DIRECTION */
.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

/* FLEX WRAP */
.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

/* FLEX GROW AND SHRINK */
.flex-grow {
  flex-grow: 1;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-1 {
  flex: 1 1 0%;
}

/* ALIGN ITEMS */
.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

/* JUSTIFY CONTENT */
.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

/* padding */
.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

/* padding y */
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

/* padding x */
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

/* padding top */
.pt-1 {
  padding-top: 0.25rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

/* padding bottom */
.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}
/* left */
.pl-1 {
  padding-left: 0.25rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

/* right */
.pr-1 {
  padding-right: 0.25rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-5 {
  padding-right: 1.25rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

/* margin */
.m-1 {
  margin: 0.25rem;
}

.m-2 {
  margin: 0.5rem;
}

.m-3 {
  margin: 0.75rem;
}

.m-4 {
  margin: 1rem;
}

.m-5 {
  margin: 1.25rem;
}

.m-6 {
  margin: 1.5rem;
}

/* margin y */
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

/* margin x */
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-5 {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}

.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}

/* margin top */
.mt-0 {
  margin-top: 0rem !important;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

/* margin bottom */
.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

/* margin left */
.ml-0 {
  margin-left: 0rem !important;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-5 {
  margin-left: 1.25rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

/* margin right */
.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-5 {
  margin-right: 1.25rem;
}

.mr-6 {
  margin-right: 1.5rem;
}
