import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import Router from "next/router";
import CommonHelper from "@/utils/CommonHelper";

const page = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }

      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  inputs: { email: "<EMAIL>", password: "<EMAIL>" },
  errors: {},
  showPassword: false,
  ref_Loading: null,
  setLoadingRef: (ref) => set({ ref_Loading: ref }),
  setInputs: (inputs) => set({ inputs }),
  setErrors: (errors) => set({ errors }),
  onTextInputListeners: (text, input) => {
    set((state) => ({ inputs: { ...state.inputs, [input]: text } }));
  },
  onTextErrorListeners: (error, input) => {
    set((state) => ({ errors: { ...state.errors, [input]: error } }));
  },
  toggleShowPassword: () =>
    set((state) => ({ showPassword: !state.showPassword })),
  onValidateInputs: () => {
    let isValid = true;
    if (!get().inputs.email) {
      get().onTextErrorListeners(
        "Email harus diisi atau tidak boleh kosong.",
        "email"
      );
      isValid = false;
    }
    if (!get().inputs.password) {
      get().onTextErrorListeners(
        "Password harus diisi atau tidak boleh kosong.",
        "password"
      );
      isValid = false;
    }

    if (isValid) {
      get().onLoginListeners(get().inputs);
    }
  },
  onLoginListeners: (params) => {
    // Check if loading ref is available
    if (get().ref_Loading) {
      get().ref_Loading.onShowDialog();

      // Here you would typically make an API call with the params
      // For now, we'll just simulate a successful login

      setTimeout(() => {
        get().ref_Loading.onCloseDialog();
        Router.replace({ pathname: "/kiosk/pos" });
      }, 500);
    } else {
      // Fallback if loading ref is not available
      Router.replace({ pathname: "/kiosk/pos" });
    }
  },
  // END: PAGE CONTENT
});

export const useLoginStore = create((...a) => ({
  ...page(...a),
}));

const useTrackedLoginStore = createTrackedSelector(useLoginStore);

export default useTrackedLoginStore;
