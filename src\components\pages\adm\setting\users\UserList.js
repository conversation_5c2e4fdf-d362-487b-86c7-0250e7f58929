// import
import React from "react";
import stylesTable from "@/styles/Table.module.css";
import Table from "@/components/libs/Table";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import UsersModalAddUpdate from "@/components/pages/adm/setting/users/UsersModalAddUpdate";
import ApiHelper from "@/utils/ApiHelper";
import Constants from "@/utils/Constants";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import Switch from "@mui/material/Switch";

const ARR_HEADING = [
  {
    key: "",
    label: "",
    className: "text-center sticky_thead",
    sort: "",
    sortable: false,
  },
  {
    key: "name",
    label: "<PERSON>a <PERSON>",
    className: "",
    sort: "asc",
    sortable: true,
  },
  {
    key: "username",
    label: "Username",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "email",
    label: "Email",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "mobilephone",
    label: "No. HP",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "role_name",
    label: "Hak Akses",
    className: "",
    sort: "",
    sortable: true,
  },
];

export default class ListUser extends React.Component {
  constructor(props) {
    super(props);
    this.ref_Table = React.createRef();
    this.state = {
      isFetched: true,
      fetchData: [],
      fetchDataShow: 0,
      fetchDataTotal: 0,
      fetchDataLimit: 10,
      fetchPageSelected: 1,
      fetchPageTotal: 0,
      inputSearch: "",
      inputSort: "name",
      inputFilter: [],
    };
  }

  componentDidMount() {
    // ...existing code...
  }

  onInit = () => {
    this.setState(
      {
        inputSearch: "",
        inputSort: "",
        inputFilter: [],
      },
      () => {
        this.onRefresh();
      }
    );
  };

  onRefresh = () => {
    this.setState(
      {
        isFetched: true,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
        fetchDataLimit: 10,
        fetchPageSelected: 1,
        fetchPageTotal: 1,
      },
      this.onFetchData
    );
  };

  onFetchData = async (isPageClicked = false) => {
    if (isPageClicked) {
      this.setState({ isFetched: true });
    }
    let response = await ApiHelper.get("kiosk/admin/user/data", {
      limit: this.state.fetchDataLimit,
      page: this.state.fetchPageSelected,
      search: this.state.inputSearch,
      sort: this.state.inputSort,
      pagination_bool: true,
    });
    if (response.status === 200) {
      this.setState({
        isFetched: false,
        fetchData: response.results.data,
        fetchDataShow:
          this.state.fetchDataShow + response.results.pagination.total_display,
        fetchDataTotal: response.results.pagination.total_data,
      });
    } else {
      this.setState({
        isFetched: false,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
      });
    }
  };

  onSubmitActivate = async (item, index) => {
    this.ref_Loading.onShowDialog();
    const response = await ApiHelper.post("kiosk/admin/user/activate", {
      id: item.id,
      active_bool: !item.active_bool,
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.onCloseDialog();
    } else {
      this.ref_MySnackbar.onNotify(
        response?.message || "Terjadi Kesalahan",
        "error"
      );
    }
    this.ref_Loading.onCloseDialog();
  };

  onSubmitDelete = async (item, index) => {
    this.ref_Loading.onShowDialog();
    const response = await ApiHelper.post("kiosk/admin/user/delete", {
      id: item.id,
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.onCloseDialog();
    } else {
      this.ref_MySnackbar.onNotify(
        response?.message || "Terjadi Kesalahan",
        "error"
      );
    }
    this.ref_Loading.onCloseDialog();
  };

  render() {
    return (
      <>
        <Table
          ref={(value) => (this.ref_Table = value)}
          customTableContainers={stylesTable.withTab}
          stylesContainer={{
            maxHeight: "calc(100% - 48px)",
          }}
          disableTitle={true}
          title={this.props.selectedTabTitle}
          subtitle={"List data dan kelola data tipe penjualan"}
          addTitle={"Data Baru"}
          addListeners={() => {
            if (this.ref_UsersModalAddUpdate?.onShowDialog) {
              this.ref_UsersModalAddUpdate.onShowDialog("add");
            }
          }}
          searchListeners={(inputSearch) => {
            this.ref_Table.setSearchValue(inputSearch);
            this.setState({ inputSearch }, () => {
              this.onRefresh();
            });
          }}
          filterListeners={() => {
            alert(`Filter Callback`);
          }}
          exportListeners={() => {
            alert(`Export Callback`);
          }}
          inputSearch={this.state.inputSearch}
          onSearch={(inputSearch) => {
            this.ref_Table.setSearchValue(inputSearch);
            this.setState({ inputSearch }, () => {
              this.onRefresh();
            });
          }}
          disabledBtnFilter
          disabledBtnExport
          sortListeners={(inputSort) => {
            this.setState({ inputSort }, () => {
              this.onRefresh();
            });
          }}
          dataHeadings={ARR_HEADING}
          dataTables={this.state.fetchData}
          renderItems={this.renderItems}
          pageCount={this.state.fetchPageTotal}
          pageSelected={this.state.fetchPageSelected}
          pageListeners={(fetchPageSelected) => {
            this.setState({ fetchPageSelected }, () => this.onFetchData(true));
          }}
          pageRow={this.state.fetchDataLimit}
          pageRowListeners={(fetchDataLimit) => {
            this.setState({ fetchDataLimit }, () => this.onFetchData(true));
          }}
          onReload={this.onRefresh}
          isFetched={this.state.isFetched || this.props.isAuthLoading}
        />

        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
        <ModalConfirmation
          ref={(value) => (this.ref_ModalConfirmation = value)}
        />

        <UsersModalAddUpdate
          ref={(value) => (this.ref_UsersModalAddUpdate = value)}
          onResults={(formType, formData, formIndex) => {
            this.onRefresh();
          }}
        />
      </>
    );
  }

  renderItems = (item, index) => {
    return (
      <tr key={index} style={{ opacity: item.active_bool ? 1 : 0.4 }}>
        <td className="text-center">
          <div className="actions">
            <Tooltip title="Ubah Data">
              <IconButton
                onClick={() => {
                  this.ref_UsersModalAddUpdate.onShowDialog(
                    "edit",
                    item,
                    index
                  );
                }}
              >
                <i className="ph ph-bold ph-pencil-simple text-blue"></i>
              </IconButton>
            </Tooltip>
            <Tooltip title="Hapus Data">
              <IconButton
                onClick={() => {
                  this.ref_ModalConfirmation.onShowDialog("delete", {
                    text: {
                      title: "Pengguna",
                      action: "Hapus",
                      info: item.name,
                    },
                    onConfirmed: (formType, formData, formIndex) => {
                      this.onSubmitDelete(item, index);
                    },
                  });
                }}
              >
                <i className="ph ph-bold ph-trash-simple text-red"></i>
              </IconButton>
            </Tooltip>
            <Tooltip
              title={item.active_bool ? "Non-aktifkan Data" : "Aktifkan Data"}
            >
              <Switch
                checked={item.active_bool}
                onClick={() => {
                  this.ref_ModalConfirmation.onShowDialog("activated", {
                    text: {
                      title: "Pengguna",
                      action: item.active_bool ? "Non-Aktifkan" : "Aktifkan",
                      info: item.name,
                    },
                    onConfirmed: (formType, formData, formIndex) => {
                      this.onSubmitActivate(item, index);
                    },
                  });
                }}
              />
            </Tooltip>
          </div>
        </td>
        <td style={{ width: "100%" }}>
          <img
            src={item.image_url || Constants.image_default.empty}
            style={{
              border: "none",
              backgroundColor: "transparent",
              width: 30,
              height: 30,
              objectFit: "contain",
            }}
          />
          {item.name || "-"}
        </td>
        <td>{item.username || "-"}</td>
        <td>{item.email || "-"}</td>
        <td>{item.mobilephone || "-"}</td>
        <td>{item.role_name || "-"}</td>
      </tr>
    );
  };
}
