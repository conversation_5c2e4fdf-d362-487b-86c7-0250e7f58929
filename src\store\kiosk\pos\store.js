import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import CommonHelper from "@/utils/CommonHelper";
import { useCheckInStore } from "./modal/storeCheckIn";

const page = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }

      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  ref_Loading: null,
  isReady: false,
  arrCategory: [],
  arrProducts: [],
  objCustomer: null,
});

// modal
const modal = (set, get) => ({
  onShowCheckIn: () => {
    useCheckInStore.getState().onShowDialog();
  },
});

export const useKioskPosStore = create((...a) => ({
  ...page(...a),
  ...modal(...a),
}));

const useTrackedKioskPosStore = createTrackedSelector(useKioskPosStore);

export default useTrackedKioskPosStore;
