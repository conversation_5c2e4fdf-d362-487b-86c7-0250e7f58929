import React from "react";
import {
  Modal,
  Box,
  TextField,
  InputAdornment,
  MenuItem,
  IconButton,
  Chip,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
  Alert,
} from "@mui/material";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";
import ImageUploadSingle from "@/components/libs/ImageUploadSingle";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Input, { numberFormatIdToNumber } from "@/components/libs/Input";
import AdminModalListProductAccurate from "@/components/pages/adm/modal/AdminModalListProductAccurate";
import ImageUploadMultiple from "@/components/libs/ImageUploadMultiple";
import InputAutoComplete2 from "@/components/libs/InputAutoComplete2";
import Table from "@/components/libs/Table";

export default class ProductModalDetail extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      detail: null,
    };
  }

  onShowDialog = (formType, formData = null, formIndex = -1) => {
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,
      },
      () => {
        this.onFetchDetail();
      }
    );
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      detail: null,
    });
  };

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get("kiosk/admin/product/detail", {
      id: this.state.formData.id,
    });

    let detail = null;
    if (response.status === 200) {
      detail = response.results.data;
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ detail });
  };

  onNotify = (message, severity = "info") => {
    this.ref_MySnackbar.onNotify(message, severity);
  };
  render() {
    let addModalClass = "large";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">Detail Produk</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderProductDetail()}</>;
  }

  renderProductDetail() {
    let objData = this.state.detail;

    if (!objData) {
      return (
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="input-form">
              <Alert severity="info">Memuat detail produk...</Alert>
            </div>
          </div>
        </div>
      );
    }

    return (
      <>
        <div className="flex-rows no-right">
          {/* Basic Product Information */}
          <div className="row wd60 no-border">
            <div className="box">
              <div className="title">Informasi Produk</div>
              <div className="detail_wrapper_grid">
                <div className="detail_container_grid">
                  <em>ID Produk</em>
                  <div className="content text">
                    {objData.product_object?.id || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Nama Produk</em>
                  <div className="content text">
                    {objData.product_object?.name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Kode/SKU</em>
                  <div className="content text">
                    {objData.product_object?.code || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Barcode</em>
                  <div className="content text">
                    {objData.product_object?.barcode || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Kategori</em>
                  <div className="content text">
                    {objData.product_object?.category_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Brand</em>
                  <div className="content text">
                    {objData.product_object?.brand_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Tipe</em>
                  <div className="content text">
                    {objData.product_object?.type || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Unit</em>
                  <div className="content text">
                    {objData.product_object?.unit_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Status Penjualan</em>
                  <div className="content">
                    <span
                      className={`status ${
                        objData.onsale_bool ? "" : "unavailable"
                      }`}
                    >
                      {objData.onsale_bool ? "Dijual" : "Tidak Dijual"}
                    </span>
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Harga Jual</em>
                  <div className="content text">
                    Rp {objData.selling_price_label || "-"}
                  </div>
                </div>
              </div>

              {objData.product_object?.notes && (
                <>
                  <div className="modal-divider"></div>
                  <div className="detail_container_grid">
                    <em>Deskripsi</em>
                    <div
                      className="content text"
                      style={{ whiteSpace: "pre-wrap" }}
                    >
                      {objData.product_object.notes}
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Etalase Information */}
            {objData.etalase_array && objData.etalase_array.length > 0 && (
              <div className="flex-rows no-right mt-4">
                <div className="row no-border">
                  <div className="box">
                    <div className="title">Kategori Produk</div>
                    <div className="input-form">
                      <div
                        style={{
                          fontSize: "0.95rem",
                          fontWeight: "600",
                          marginBottom: "0.5rem",
                        }}
                      >
                        Label Kategori: {objData.etalase_label || "-"}
                      </div>
                      {objData.etalase_array.map((etalase, etalaseIndex) => (
                        <div key={etalaseIndex} className="input-form">
                          <div
                            style={{
                              backgroundColor: "#f8f9fa",
                              padding: "1rem",
                              borderRadius: "6px",
                              border: "1px solid #e9ecef",
                            }}
                          >
                            <div
                              style={{
                                fontSize: "0.9rem",
                                fontWeight: "600",
                                marginBottom: "0.5rem",
                                color: "#495057",
                              }}
                            >
                              Grup Kategori {etalaseIndex + 1}
                            </div>
                            <div
                              style={{
                                display: "flex",
                                flexWrap: "wrap",
                                gap: "0.5rem",
                              }}
                            >
                              {etalase.categories
                                ?.filter((cat) => cat !== null)
                                .map((category, catIndex) => (
                                  <Chip
                                    key={category.id || catIndex}
                                    label={category.name}
                                    size="small"
                                    variant="outlined"
                                    style={{
                                      backgroundColor:
                                        category.etalase_group === "0"
                                          ? "#e3f2fd"
                                          : "#f3e5f5",
                                      borderColor:
                                        category.etalase_group === "0"
                                          ? "#2196f3"
                                          : "#9c27b0",
                                    }}
                                  />
                                ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Variants Information */}
            {objData.variant_array && objData.variant_array.length > 0 && (
              <div className="flex-rows no-right">
                <div className="row no-border">
                  <div className="box">
                    <div className="title">Varian Produk</div>
                    <div className="input-form">
                      <Alert severity="info">
                        Produk ini memiliki {objData.variant_array.length}{" "}
                        varian
                      </Alert>
                      {/* You can expand this section to show variant details if needed */}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Product Images */}
          <div className="row wd40 no-border">
            <div className="box">
              <div className="title">Gambar Produk</div>
              {objData.main_image_image_url ? (
                <div className="input-form">
                  <div style={{ textAlign: "center", marginBottom: "1rem" }}>
                    <img
                      src={objData.main_image_image_url}
                      alt="Main Product Image"
                      style={{
                        maxWidth: "100%",
                        height: "auto",
                        borderRadius: "8px",
                        border: "2px solid #e9e9e9",
                      }}
                    />
                    <div
                      style={{
                        marginTop: "0.5rem",
                        fontSize: "0.9rem",
                        color: "#666",
                      }}
                    >
                      Gambar Utama
                    </div>
                  </div>
                </div>
              ) : (
                <div className="input-form">
                  <Alert severity="info">Tidak ada gambar utama</Alert>
                </div>
              )}

              {objData.image_array && objData.image_array.length > 1 && (
                <>
                  <div className="modal-divider"></div>
                  <div className="input-form">
                    <div
                      style={{
                        fontSize: "0.95rem",
                        fontWeight: "600",
                        marginBottom: "0.5rem",
                      }}
                    >
                      Galeri Gambar ({objData.image_array.length} gambar)
                    </div>
                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns:
                          "repeat(auto-fill, minmax(80px, 1fr))",
                        gap: "0.5rem",
                      }}
                    >
                      {objData.image_array.map((image, index) => (
                        <div
                          key={image.id || index}
                          style={{ position: "relative" }}
                        >
                          <img
                            src={image.image_url}
                            alt={`Product Image ${index + 1}`}
                            style={{
                              width: "100%",
                              height: "80px",
                              objectFit: "cover",
                              borderRadius: "4px",
                              border:
                                index === objData.main_image_index
                                  ? "2px solid #3498db"
                                  : "1px solid #e9e9e9",
                            }}
                          />
                          {index === objData.main_image_index && (
                            <div
                              style={{
                                position: "absolute",
                                top: "2px",
                                right: "2px",
                                backgroundColor: "#3498db",
                                color: "white",
                                fontSize: "0.7rem",
                                padding: "2px 4px",
                                borderRadius: "2px",
                              }}
                            >
                              Utama
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button className="button ml-0" onClick={() => this.onCloseDialog()}>
          <i className="ph ph-bold ph-x"></i>
          <span>Tutup</span>
        </button>
      </>
    );
  }
}
