import React from "react";
import {
  Modal,
  Box,
  TextField,
  InputAdornment,
  MenuItem,
  IconButton,
  Chip,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
  Alert,
} from "@mui/material";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";
import ImageUploadSingle from "@/components/libs/ImageUploadSingle";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Input, { numberFormatIdToNumber } from "@/components/libs/Input";
import AdminModalListProductAccurate from "@/components/pages/adm/modal/AdminModalListProductAccurate";
import ImageUploadMultiple from "@/components/libs/ImageUploadMultiple";
import InputAutoComplete2 from "@/components/libs/InputAutoComplete2";
import Table from "@/components/libs/Table";

export default class ProductModalDetail extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      detail: null,
    };
  }

  onShowDialog = (formType, formData = null, formIndex = -1) => {
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,
      },
      () => {
        this.onFetchDetail();
      }
    );
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      detail: null,
    });
  };

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get("kiosk/admin/product/detail", {
      id: this.state.formData.id,
    });

    let detail = null;
    if (response.status === 200) {
      detail = response.results.data;
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ detail });
  };
  render() {
    let addModalClass = "large";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">
          {this.state.formType === "add" && `Tambah Data Produk`}
          {this.state.formType === "edit" && `Ubah Data Produk`}
        </div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    let objData = this.state.detail;
    // {
    // 	"id": "1",
    // 	"par_id": "0",
    // 	"product_id": "27987",
    // 	"onsale_bool": true,
    // 	"etalase_label": "Laptop / Laptop By Brand / Acer / Aspire",
    // 	"main_image_id": "7",
    // 	"selling_price": 6499000,
    // 	"product_object": {
    // 		"id": "27987",
    // 		"image_url": "",
    // 		"name": "Acer A14-51M-31RN Grey W11 OHS Core 3 100U",
    // 		"code": "NB-AC-A14-51M-31RN",
    // 		"barcode": "",
    // 		"notes": "",
    // 		"type": "Persediaan",
    // 		"brand_id": "586",
    // 		"brand_accurate_id": "454",
    // 		"brand_name": "ACER",
    // 		"category_id": "194",
    // 		"category_accurate_id": "303",
    // 		"category_name": "101NOTAC",
    // 		"category_title": "",
    // 		"unit_accurate_id": "200",
    // 		"unit_name": "UNIT"
    // 	},
    // 	"selling_price_label": "6.499.000",
    // 	"variant_array": [],
    // 	"par_object": null,
    // 	"main_image_index": 0,
    // 	"main_image_image_url": "https://els.id/wp-content/uploads/2024/03/A14-51M.jpg",
    // 	"image_array": [
    // 		{
    // 			"id": "7",
    // 			"image_url": "https://els.id/wp-content/uploads/2024/03/A14-51M.jpg"
    // 		},
    // 		{
    // 			"id": "8",
    // 			"image_url": "https://els.id/wp-content/uploads/2024/03/A14-51M-6.jpg"
    // 		},
    // 		{
    // 			"id": "9",
    // 			"image_url": "https://els.id/wp-content/uploads/2024/03/A14-51M-5.jpg"
    // 		},
    // 		{
    // 			"id": "10",
    // 			"image_url": "https://els.id/wp-content/uploads/2024/03/A14-51M-4.jpg"
    // 		},
    // 		{
    // 			"id": "11",
    // 			"image_url": "https://els.id/wp-content/uploads/2024/03/A14-51M-3.jpg"
    // 		},
    // 		{
    // 			"id": "12",
    // 			"image_url": "https://els.id/wp-content/uploads/2024/03/A14-51M-2.jpg"
    // 		}
    // 	],
    // 	"etalase_array": [
    // 		{
    // 			"categories": [
    // 				{
    // 					"id": "17",
    // 					"par_id": "0",
    // 					"have_child_bool": true,
    // 					"name": "Laptop",
    // 					"image_url": "",
    // 					"etalase_group": "0"
    // 				},
    // 				{
    // 					"id": "34",
    // 					"par_id": "17",
    // 					"have_child_bool": true,
    // 					"name": "Laptop By Brand",
    // 					"image_url": "",
    // 					"etalase_group": "0"
    // 				},
    // 				{
    // 					"id": "35",
    // 					"par_id": "34",
    // 					"have_child_bool": true,
    // 					"name": "Acer",
    // 					"image_url": "",
    // 					"etalase_group": "0"
    // 				},
    // 				{
    // 					"id": "40",
    // 					"par_id": "35",
    // 					"have_child_bool": false,
    // 					"name": "Aspire",
    // 					"image_url": "",
    // 					"etalase_group": "0"
    // 				}
    // 			]
    // 		},
    // 		{
    // 			"categories": [
    // 				{
    // 					"id": "17",
    // 					"par_id": "0",
    // 					"have_child_bool": true,
    // 					"name": "Laptop",
    // 					"image_url": "",
    // 					"etalase_group": "1"
    // 				},
    // 				{
    // 					"id": "20",
    // 					"par_id": "17",
    // 					"have_child_bool": true,
    // 					"name": "Laptop Intel",
    // 					"image_url": "",
    // 					"etalase_group": "1"
    // 				},
    // 				{
    // 					"id": "22",
    // 					"par_id": "20",
    // 					"have_child_bool": false,
    // 					"name": "Core I5",
    // 					"image_url": "",
    // 					"etalase_group": "1"
    // 				},
    // 				null
    // 			]
    // 		}
    // 	]
    // }

    return (
      <>
        {objData && (
          <div className="box mt-1 flex flex-col gap-4 py-4 px-4">
            <div className="detail_wrapper_grid">
              <div className="detail_container_grid">
                <em>Nama</em>
                <div className="content text">
                  {objData.product_object.name}
                </div>
              </div>
              <div className="detail_container_grid">
                <em>Kode/SKU</em>
                <div className="content text">
                  {objData.product_object.code || "-"}
                </div>
              </div>
              <div className="detail_container_grid">
                <em>Kategori</em>
                <div className="content text">
                  {objData.product_object.category_name || "-"}
                </div>
              </div>
              <div className="detail_container_grid">
                <em>Brand</em>
                <div className="content text">
                  {objData.product_object.brand_name || "-"}
                </div>
              </div>
              <div className="detail_container_grid">
                <em>Tipe</em>
                <div className="content text">
                  {objData.product_object.type || "-"}
                </div>
              </div>
              <div className="detail_container_grid">
                <em>Deskripsi</em>
                <div className="content text">
                  {objData.product_object.notes || "-"}
                </div>
              </div>
            </div>
          </div>
          <div className="box mt-1 flex flex-col gap-4 py-4 px-4">
            <div className="detail_wrapper_grid">
              <div className="detail_container_grid">
                <em>Harga Jual</em>
                <div className="content text">
                  {objData.selling_price_label || "-"}
                </div>
              </div>
              <div className="detail_container_grid">
                <em>Stok</em>
                <div className="content text">0</div>
              </div>
            </div>
          </div>
          <div className="box mt-1 flex flex-col gap-4 py-4 px-4">
            <div className="detail_wrapper_grid">
              <div className="detail_container_grid">
                <em>Gambar</em>
                <div className="content text">
                  <img
                    src={objData.main_image_image_url}
                    style={{
                      border: "none",
                      backgroundColor: "transparent",
                      width: 30,


        )}
        
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button className="button ml-0" onClick={() => this.onCloseDialog()}>
          <i className="ph ph-bold ph-x"></i>
          <span>Tutup</span>
        </button>
      </>
    );
  }
}
