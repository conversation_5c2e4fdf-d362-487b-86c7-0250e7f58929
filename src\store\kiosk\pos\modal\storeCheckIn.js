import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import { useKioskPosStore } from "../store";
import Router from "next/router";

const state = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }

      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  showDialog: false,
  formType: "checkin",
  formData: null,
  isChecked: false,
  isVerified: false,
  inputs: {
    customer_type_id: 1,
    customer_type_name: "Member",
    customer_id: 1,
    customer_name: "<PERSON> Doe",
    customer_image_url:
      "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQGXSeTomhrdIan6G5XRR_rk8zJ12BjlmXAG__1AnicoI7PC5HtZe9s26QERd2FqObM6sw&usqp=CAU",
    customer_email_address: "<EMAIL>",
    customer_wa_number: "085200000000",
  },
  errors: {},

  onShowDialog: (formType = "checkin", formData = null) => {
    set({
      formType,
      formData,
      showDialog: true,
      isChecked: false,
      isVerified: false,
    });
    if (get().inputs.customer_type_id === 1) {
      setTimeout(() => {
        const inputWhatsapp = document.getElementById("input-whatsapp");
        if (inputWhatsapp) {
          inputWhatsapp.focus();
        }
      }, 250);
    }
  },
  onCloseDialog: () => {
    set({ showDialog: false, formType: "checkin", formData: null, errors: {} });
  },
  onCheckListeners: () => {
    // Simulate loading and checking logic
    setTimeout(() => {
      set({ isChecked: true, isVerified: false });
      setTimeout(() => {
        const inputOtp = document.getElementById("input-otp");
        if (inputOtp) {
          inputOtp.focus();
        }
      }, 250);
    }, 500);
  },
  onVerifiedListeners: () => {
    // Simulate verification logic
    setTimeout(() => {
      set({ isVerified: true });
    }, 500);
  },
  onNextListeners: () => {
    // Simulate next step logic
    setTimeout(() => {
      get().onCloseDialog();
      if (get().onSubmit) {
        get().onSubmit(get().inputs);
      }
    }, 500);
  },

  onTextInputListeners: (text, input) => {
    set((state) => ({ inputs: { ...state.inputs, [input]: text } }));
  },
  onTextErrorListeners: (error, input) => {
    set((state) => ({ errors: { ...state.errors, [input]: error } }));
  },

  onSubmit: (objCustomer) => {
    useKioskPosStore.getState().setState({ objCustomer });
    Router.push({ pathname: `/kiosk/pos/payment` });
  },
});

export const useCheckInStore = create((...a) => ({
  ...state(...a),
}));

const useTrackedCheckInStore = createTrackedSelector(useCheckInStore);

export default useTrackedCheckInStore;
