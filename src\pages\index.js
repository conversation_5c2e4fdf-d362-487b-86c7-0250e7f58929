/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import React, { useEffect, useRef } from "react";
import styles from "@/styles/Pos.module.css";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";
import AuthWrapper from "@/components/wrapper/AuthWrapper";
import useTrackedLoginStore from "@/store/login/store";

const KioskLogin = () => {
  const { setLoadingRef } = useTrackedLoginStore();
  const loadingRef = useRef(null);

  useEffect(() => {
    // Set loading ref in the store
    if (loadingRef.current) {
      setLoadingRef(loadingRef.current);
    }

    // Focus on email input
    setTimeout(() => {
      const inputEmail = document.getElementById("input-email");
      if (inputEmail) {
        inputEmail.focus();
      }
    }, 250);
  }, [setLoadingRef]);

  return (
    <div className={`${styles.lg_containers}`}>
      <div className={styles.contents}>
        <RenderForm />
      </div>

      <Loading ref={loadingRef} />
    </div>
  );
};

// RenderForm
const RenderForm = () => {
  const { onValidateInputs } = useTrackedLoginStore();

  return (
    <div className={styles.form}>
      <img alt={Constants.appName} src="/assets/images/logo-small.png" />

      <h1 className="text-center">Kiosk Els</h1>

      <RenderFormInputEmail />
      <RenderFormInputPassword />

      <button className={styles.submit} onClick={onValidateInputs}>
        Login Kiosk
      </button>

      <div className={styles.info}>
        <p className="text-center">2025 © ELS</p>
      </div>
    </div>
  );
};

const RenderFormInputEmail = () => {
  const { inputs, errors, onTextInputListeners, onTextErrorListeners } =
    useTrackedLoginStore();

  return (
    <div className={`${styles.inputs} ${errors.email && styles.errors}`}>
      <div className={styles.label}>
        <i className="ph ph-bold ph-envelope-simple"></i>
      </div>
      <input
        id="input-email"
        placeholder="Email Anda"
        value={inputs.email || ""}
        onFocus={() => onTextErrorListeners(null, "email")}
        onChange={(e) => onTextInputListeners(e.target.value, "email")}
        autoComplete="off"
      />
      {errors.email && (
        <div className={styles.errIcon}>
          <i className={`ph ph-bold ph-warning-circle`}></i>
          <span>{errors.email || "Email tidak terdaftar."}</span>
        </div>
      )}
    </div>
  );
};

const RenderFormInputPassword = () => {
  const {
    inputs,
    errors,
    showPassword,
    onTextInputListeners,
    onTextErrorListeners,
    toggleShowPassword,
  } = useTrackedLoginStore();

  return (
    <div className={`${styles.inputs} ${errors.password && styles.errors}`}>
      <div className={styles.label}>
        <i className="ph ph-bold ph-lock"></i>
      </div>
      <input
        id="input-password"
        placeholder="Password Anda"
        value={inputs.password || ""}
        onFocus={() => onTextErrorListeners(null, "password")}
        onChange={(e) => onTextInputListeners(e.target.value, "password")}
        type={showPassword ? "text" : "password"}
      />
      {errors.password ? (
        <div className={styles.errIcon}>
          <i className={`ph ph-bold ph-warning-circle`}></i>
          <span>{errors.password || "Password tidak sesuai."}</span>
        </div>
      ) : (
        <button onClick={toggleShowPassword}>
          <i
            className={`ph ph-bold ${
              !showPassword ? "ph-eye-slash" : "ph-eye"
            }`}
          ></i>
        </button>
      )}
    </div>
  );
};

export default AuthWrapper(KioskLogin, {
  redirectTo: Constants.webUrl.home,
  redirectIfFound: true,
});
