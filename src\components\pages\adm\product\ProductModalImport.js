import React from "react";
import ExcelJS from "exceljs";
import {
  Modal,
  Box,
  TextField,
  InputAdornment,
  MenuItem,
  Alert,
} from "@mui/material";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";
import <PERSON><PERSON>Helper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Input from "@/components/libs/Input";
import InputAutoComplete2 from "@/components/libs/InputAutoComplete2";
import FilterHelper, {
  FILTER_TYPE_STRING,
  FILTER_COMP_EQUAL,
} from "@/utils/FilterHelper";
import FileUploadSingle2 from "@/components/libs/FileUploadSingle2";

const DEFAULT_INPUTS = {
  file: null,
};

export default class ProductModalImport extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    };
  }

  onShowDialog = (
    formType,
    formData = null,
    formIndex = -1,
    formInputs = null
  ) => {
    let inputs = structuredClone(DEFAULT_INPUTS);
    if (formInputs) {
      inputs = formInputs;
    }
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,

        inputs,
      },
      () => {
        setTimeout(() => {
          var inputNameID = document.getElementById("input-name");
          if (inputNameID) {
            inputNameID.focus();
          }
        }, 250);
      }
    );
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    });
  };

  onDownloadTemplateListeners = async () => {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("Product Template");

      // Define columns
      worksheet.columns = [
        { header: "Kode/SKU Produk", key: "code", width: 30 },
        {
          header: "Tambahkan Gambar Produk dari Els.Id",
          key: "get_img",
          width: 50,
        },
      ];

      // Add header row
      worksheet.getRow(1).font = { bold: true };

      // add data example
      worksheet.addRow(["123456", "Ya"]);
      worksheet.addRow(["123456", "Tidak"]);

      // Generate Excel file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "Template Import Produk.xlsx";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      this.ref_MySnackbar.current.showSnackbar({
        message: "Gagal download template: " + error.message,
        severity: "error",
      });
    }
  };

  onValidateListeners = async () => {
    if (!this.state.inputs.file) {
      this.ref_MySnackbar.onNotify("File belum dipilih", "error");
      return;
    }

    this.onSubmit();
  };

  onSubmit = async () => {
    this.ref_Loading.onShowDialog();
    let formData = new FormData();
    formData.append("file", this.state.inputs.file);
    formData.append("aol_id", this.state.formData.aol_id);
    formData.append(
      "aol_session_database",
      this.state.formData.aol_session_database
    );
    let response = await ApiHelper.uploadPOST(
      "kiosk/admin/product/import",
      formData
    );
    if (response.status === 200) {
      this.ref_Loading.onCloseDialog();

      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();

      this.ref_MySnackbar.onNotify(
        response?.message || "Berhasil Simpan Data",
        "success"
      );
    } else {
      this.ref_Loading.onCloseDialog();
      this.ref_MySnackbar.onNotify(
        response?.message || "Terjadi Kesalahan",
        "error"
      );
    }
  };

  render() {
    let addModalClass = "small";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">Import Data</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderForm()}</>;
  }

  renderFooter() {
    return (
      <>
        <button
          className="button"
          onClick={() => {
            this.onValidateListeners();
          }}
        >
          <i className="ph ph-bold ph-check-circle"></i>
          <span>Import</span>
        </button>
        <button className="button cancel" onClick={() => this.onCloseDialog()}>
          <i className="ph ph-bold ph-x-circle"></i>
          <span>Batal</span>
        </button>
      </>
    );
  }

  renderForm() {
    return (
      <>
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="input-form">
              {/* Alert download template terlebih dahulu */}
              <Alert severity="info">
                Unduh dan sesuaikan template import terlebih dahulu sebelum
                mengimpor data. Jika data sudah ada dan kolom "Tambahkan Gambar
                Produk dari Els.Id" diisi dengan "Ya", maka gambar yang belum
                ada akan ditambahkan.
              </Alert>
            </div>
            {/* Template Download */}
            <div className="input-form">
              <button
                className="button ml-0 mt-4"
                onClick={() => this.onDownloadTemplateListeners()}
              >
                <i className="ph ph-bold ph-download"></i>
                <span>Unduh Template</span>
              </button>
            </div>
            <div className="input-form">
              <FileUploadSingle2
                id={"file"}
                title={"File Import"}
                placeholder={"Pilih file..."}
                accept={".xlsx"}
                maxSizeMB={5}
                onCompleted={(file) => {
                  let inputs = structuredClone(this.state.inputs);
                  inputs.file = file;
                  this.setState({ inputs });
                }}
                style={{ height: "200px" }}
                required
              />
            </div>
          </div>
        </div>
      </>
    );
  }
}
