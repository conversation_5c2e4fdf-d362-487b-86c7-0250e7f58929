/*
Created by esoda
Created on Mei, 2025
Contact esoda.id
*/

import React from "react";
import stylesTable from "@/styles/Table.module.css";
import Table from "@/components/libs/Table";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import ProductModalAddUpdate from "@/components/pages/adm/product/ProductModalAddUpdate";
import ApiHelper from "@/utils/ApiHelper";
import Constants from "@/utils/Constants";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import Switch from "@mui/material/Switch";
import CommonHelper from "@/utils/CommonHelper";
import ProductModalListFilter from "@/components/pages/adm/product/ProductModalListFilter";
import Router from "next/router";
import { FILTER_COMP_EQUAL, FILTER_TYPE_STRING } from "@/utils/const/FILTER";
import FilterHelper from "@/utils/FilterHelper";
import ProductModalImport from "./ProductModalImport";
import ProductModalDetail from "./ProductModalDetail";

const ARR_HEADING = [
  {
    key: "",
    label: "",
    className: "text-center wd40 sticky_thead",
    sortable: false,
    sort: "",
  },
  {
    key: "name",
    label: "Nama Produk",
    className: "",
    sortable: true,
    sort: "asc",
  },
  {
    key: "code",
    label: "Kode Produk",
    className: "",
    sortable: true,
    sort: "",
  },
  {
    key: "etalase_label",
    label: "Kategori",
    className: "",
    sortable: false,
    sort: "",
  },
  {
    key: "brand_name",
    label: "Brand",
    className: "",
    sortable: true,
    sort: "",
  },
  {
    key: "selling_price",
    label: "Harga Jual",
    className: "",
    sortable: true,
    sort: "",
  },
  {
    key: "onsale_bool",
    label: "Dijual",
    className: "",
    sortable: true,
    sort: "",
  },
  {
    key: "notes",
    label: "Deskripsi",
    className: "",
    sortable: true,
    sort: "",
  },
];

export default class ProductList extends React.Component {
  constructor(props) {
    super(props);
    this.ref_Table = React.createRef();
    this.state = {
      isFetched: true,
      fetchData: [],
      fetchDataShow: 0,
      fetchDataTotal: 0,
      fetchDataLimit: 10,
      fetchPageSelected: 1,
      fetchPageTotal: 0,
      inputSearch: "",
      inputSort: "name",
      inputFilter: [],
      inputFilterObj: {},
      filterInput: {},
    };
  }

  componentDidMount() {}

  onInit = () => {
    this.setState(
      {
        inputSearch: "",
        inputSort: "",
        inputFilter: [],
      },
      () => {
        this.onRefresh();
      }
    );
  };

  onRefresh = () => {
    this.setState(
      {
        isFetched: true,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
        fetchDataLimit: 10,
        fetchPageSelected: 1,
        fetchPageTotal: 1,
      },
      this.onFetchData
    );
  };

  onFetchData = async (isPageClicked = false) => {
    if (isPageClicked) {
      this.setState({ isFetched: true });
    }

    let params = {
      limit: this.state.fetchDataLimit,
      page: this.state.fetchPageSelected,
      search: this.state.inputSearch,
      sort: this.state.inputSort,
      ...this.state.inputFilterObj,
      pagination_bool: true,
    };

    let response = await ApiHelper.get("kiosk/admin/product/data", params);
    if (response.status === 200) {
      this.setState({
        isFetched: false,
        fetchData: response.results.data,
        fetchDataShow:
          this.state.fetchDataShow + response.results.pagination.total_display,
        fetchDataTotal: response.results.pagination.total_data,
      });
    } else {
      this.setState({
        isFetched: false,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
      });
    }
  };

  onSubmitOnSale = async (item, index) => {
    this.ref_Loading.onShowDialog();
    const response = await ApiHelper.post("kiosk/admin/product/sellable", {
      id: item.id,
      onsale_bool: !item.onsale_bool,
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.onCloseDialog();
    } else {
      this.ref_MySnackbar.onNotify(
        response?.message || "Terjadi Kesalahan",
        "error"
      );
    }
    this.ref_Loading.onCloseDialog();
  };

  onSubmitDelete = async (item, index) => {
    this.ref_Loading.onShowDialog();
    const response = await ApiHelper.post("kiosk/admin/product/delete", {
      id: item.id,
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.onCloseDialog();
    } else {
      this.ref_MySnackbar.onNotify(
        response?.message || "Terjadi Kesalahan",
        "error"
      );
    }
    this.ref_Loading.onCloseDialog();
  };

  // ====================================================================================
  // ========== FILTER ==================================================================
  // ====================================================================================
  onFilterListeners = () => {
    this.ref_ProductModalListFilter.onShowDialog(
      "filter",
      {
        inputs: this.state.filterInput,
        dataParams: {
          aol_id: this.props.selected_aol.aol_id,
          aol_session_database: this.props.selected_aol.database,
        },
      },
      -1
    );
  };

  onRemoveFilterListeners = (index) => {
    let filterInput = structuredClone(this.state.filterInput);
    let inputFilterObj = structuredClone(this.state.inputFilterObj);
    let inputFilter = structuredClone(this.state.inputFilter);

    inputFilter.forEach((filter, filterIdx) => {
      if (filterIdx === index) {
        for (const key in filter.removeFieldObj) {
          const element = filter.removeFieldObj[key];
          delete inputFilterObj[element];
          delete filterInput[element];
        }
        inputFilter.splice(filterIdx, 1);
      }
    });

    this.setState({ inputFilter, inputFilterObj, filterInput }, this.onRefresh);
  };

  onFilterChangeListeners = (filterInput) => {
    // refFilter
    const refFilter = {
      etalase: {
        field: (filter, key) => {
          return "etalase_id";
        },
        fieldObj: (filter, key) => {
          if (!filter?.id) {
            return {};
          }
          return {
            [`etalase_id`]: filter.id,
          };
        },
        removeFieldObj: [`etalase`, `etalase_id`],
        value: (filter, key) => {
          if (!filter?.id) {
            return "";
          }
          return `${filter.id}`;
        },
        title: (filter, key) => {
          if (!filter?.name) {
            return ``;
          }
          return `Kategori Produk: ${filter.name}`;
        },
        type: FILTER_TYPE_STRING,
        comparison: FILTER_COMP_EQUAL,
      },
      brand: {
        field: (filter, key) => {
          return "brand_id";
        },
        fieldObj: (filter, key) => {
          if (!filter?.id) {
            return {};
          }
          return {
            [`brand_id`]: filter.id,
          };
        },
        removeFieldObj: [`brand`, `brand_id`],
        value: (filter, key) => {
          if (!filter?.id) {
            return "";
          }
          return `${filter.id}`;
        },
        title: (filter, key) => {
          if (!filter?.name) {
            return ``;
          }
          return `Brand: ${filter.name}`;
        },
        type: FILTER_TYPE_STRING,
        comparison: FILTER_COMP_EQUAL,
      },
    };
    const filterHelper = new FilterHelper();
    const { inputFilter, inputFilterObj } = filterHelper.generateFilter(
      filterInput,
      refFilter
    );

    this.setState({ inputFilter, inputFilterObj, filterInput }, this.onRefresh);
  };

  // ====================================================================================
  // ========== FILTER ==================================================================
  // ====================================================================================

  onImportListeners = () => {
    this.ref_ProductModalImport.onShowDialog("", {
      aol_id: this.props.selected_aol.aol_id,
      aol_session_database: this.props.selected_aol.database,
    });
  };

  render() {
    return (
      <>
        <Table
          ref={(value) => (this.ref_Table = value)}
          customTableContainers={stylesTable.withTab}
          disableTitle={true}
          title={this.props.selectedTabTitle}
          subtitle={"List data dan kelola data produk"}
          addTitle={"Data Baru"}
          addListeners={() => {
            if (this.ref_ProductModalAddUpdate?.onShowDialog) {
              this.ref_ProductModalAddUpdate.onShowDialog("add");
            }
          }}
          searchListeners={(inputSearch) => {
            this.ref_Table.setSearchValue(inputSearch);
            this.setState({ inputSearch }, () => {
              this.onRefresh();
            });
          }}
          exportListeners={() => {
            alert(`Export Callback`);
          }}
          sortListeners={(inputSort) => {
            this.setState({ inputSort }, () => {
              this.onRefresh();
            });
          }}
          inputSearch={this.state.inputSearch}
          onSearch={(inputSearch) => {
            this.ref_Table.setSearchValue(inputSearch);
            this.setState({ inputSearch }, () => {
              this.onRefresh();
            });
          }}
          disabledBtnExport
          dataHeadings={ARR_HEADING}
          dataTables={this.state.fetchData}
          renderItems={this.renderItems}
          pageCount={this.state.fetchPageTotal}
          pageSelected={this.state.fetchPageSelected}
          pageListeners={(fetchPageSelected) => {
            this.setState({ fetchPageSelected }, () => this.onFetchData(true));
          }}
          pageRow={this.state.fetchDataLimit}
          pageRowListeners={(fetchDataLimit) => {
            this.setState({ fetchDataLimit }, () => this.onFetchData(true));
          }}
          onReload={this.onRefresh}
          isFetched={this.state.isFetched}
          filterListeners={() => {
            this.onFilterListeners();
          }}
          inputFilter={this.state.inputFilter}
          onRemoveFilterItem={this.onRemoveFilterListeners}
          disabledBtnImport={false}
          importListeners={this.onImportListeners}
        />
        <ProductModalAddUpdate
          ref={(value) => (this.ref_ProductModalAddUpdate = value)}
          onResults={this.onRefresh}
          selected_aol={this.props.selected_aol}
        />
        <ModalConfirmation
          ref={(value) => (this.ref_ModalConfirmation = value)}
        />
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />

        <ProductModalListFilter
          ref={(value) => (this.ref_ProductModalListFilter = value)}
          onFilter={this.onFilterChangeListeners}
        />

        <ProductModalImport
          ref={(value) => (this.ref_ProductModalImport = value)}
          onResults={this.onRefresh}
        />

        <ProductModalDetail
          ref={(value) => (this.ref_ProductModalDetail = value)}
        />
      </>
    );
  }

  renderItems = (item, index) => {
    return (
      <tr key={index} style={{ opacity: item.onsale_bool ? 1 : 0.4 }}>
        <td className="text-center">
          <div className="actions">
            <Tooltip title="Lihat Detail">
              <IconButton
                onClick={() => {
                  this.ref_ProductModalDetail.onShowDialog(
                    "detail",
                    item,
                    index
                  );
                }}
              >
                <i className="ph ph-bold ph-eye text-blue"></i>
              </IconButton>
            </Tooltip>
            <Tooltip title="Ubah Data">
              <IconButton
                onClick={() => {
                  this.ref_ProductModalAddUpdate.onShowDialog(
                    "edit",
                    item,
                    index
                  );
                }}
              >
                <i className="ph ph-bold ph-pencil-simple text-blue"></i>
              </IconButton>
            </Tooltip>
            <Tooltip title="Hapus Data">
              <IconButton
                onClick={() => {
                  this.ref_ModalConfirmation.onShowDialog("delete", {
                    text: {
                      title: "Produk",
                      action: "Hapus",
                      info: item.name,
                    },
                    onConfirmed: (formType, formData, formIndex) => {
                      this.onSubmitDelete(item, index);
                    },
                  });
                }}
              >
                <i className="ph ph-bold ph-trash-simple text-red"></i>
              </IconButton>
            </Tooltip>
            <Tooltip
              title={
                item.onsale_bool
                  ? "Jadikan Tidak Bisa Dijual"
                  : "Jadikan Bisa Dijual"
              }
            >
              <Switch
                checked={item.onsale_bool}
                onClick={() => {
                  this.ref_ModalConfirmation.onShowDialog("custom", {
                    icon: (
                      <>
                        <i className="ph ph-bold ph-check-circle"></i>
                      </>
                    ),
                    text: {
                      title: item.onsale_bool
                        ? "Jadikan Tidak Bisa Dijual"
                        : "Jadikan Bisa Dijual",
                      action: "Ya",
                      info: (
                        <>
                          {`Jadikan produk ${item.name} ${
                            item.onsale_bool
                              ? "Tidak Bisa Dijual"
                              : "Bisa Dijual"
                          }`}
                        </>
                      ),
                    },
                    onConfirmed: (formType, formData, formIndex) => {
                      this.onSubmitOnSale(item, index);
                    },
                  });
                  // this.ref_ModalConfirmation.onShowDialog("activated", {
                  //   text: {
                  //     title: "Produk",
                  //     action: item.active_bool ? "Non-Aktifkan" : "Aktifkan",
                  //     info: item.name,
                  //   },
                  //   onConfirmed: (formType, formData, formIndex) => {
                  //     this.onSubmitOnSale(item, index);
                  //   },
                  // });
                }}
              />
            </Tooltip>
          </div>
        </td>
        <td style={{ width: "100%" }}>{item.name || "-"}</td>
        <td>{item.code || "-"}</td>
        <td>{item.etalase_label || "-"}</td>
        <td>{item.brand_name || "-"}</td>
        <td>
          {item.selling_price
            ? CommonHelper.formatNumber(item.selling_price, "idr")
            : "Rp0" || "-"}
        </td>
        <td style={{ textAlign: "center" }}>
          <span
            style={{
              color: item.onsale_bool
                ? "var(--success-color)"
                : "var(--error-color)",
              fontWeight: "bold",
            }}
          >
            {item.onsale_bool ? "Ya" : "Tidak"}
          </span>
        </td>
        <td>{item.notes || "-"}</td>
      </tr>
    );
  };
}
