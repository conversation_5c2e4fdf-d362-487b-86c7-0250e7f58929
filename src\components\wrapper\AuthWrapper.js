import useAuth from "@/hook/useAuth";
import React, { useEffect, useState } from "react";

export default function AuthWrapper(WrappedComponent, wrappedProps) {
  return function (props) {

    let redirectTo = "";
    if (wrappedProps?.redirectTo) {
      redirectTo = wrappedProps?.redirectTo;
    }
    let redirectIfFound = false;
    if (wrappedProps?.redirectIfFound) {
      redirectIfFound = wrappedProps?.redirectIfFound;
    }
    let mustLogin = false;
    if (wrappedProps?.mustLogin) {
      mustLogin = wrappedProps?.mustLogin;
    }

    const { mutateUser, user, isAuthLoading } = useAuth({
      redirectTo,
      redirectIfFound,
      checkUrl: "kiosk/auth/check",
    });

    // console.log("user", WrappedComponent.name, user, isAuthLoading);
    useEffect(() => {
      return () => {};
    }, [isAuthLoading]);

    return (
      <>
        <WrappedComponent
          {...props}
          isAuthLoading={isAuthLoading}
          isLoggedIn={user?.isLoggedIn}
          user={user?.user || null}
          selected_aol={user?.selected_aol || null}
          selected_branch={user?.selected_branch || null}
          menu={user?.menu || null}
        />
      </>
    );
  };
}
