/*
Created by esoda
Created on Feb, 2025
Contact esoda.id
*/

import React from "react";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import CommonHelper from "@/utils/CommonHelper";
import Constants from "@/utils/Constants";
import styles from "@/styles/Kiosk.module.css";
import TimePicker from "@/components/TimePicker";
import TransactionSalesorderCashierPayment from "@/components/pages/kiosk/pos/payment/cashierPayment";
import Loading from "@/components/modal/Loading";
import Router from "next/router";

export default class TransactionSalesorderKioskPayment extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      arrCart: [
        {
          id: 1,
          image_url:
            "https://www.thenextsole.com/storage/images/AQ2731-104.png",
          name: "Nike Joyride Run Flyknit Women's Running Shoes",
          price: 12500000,
          unit: "pcs",
          qty: 1,
          desc: "",
        },
        {
          id: 2,
          image_url:
            "https://static.nike.com/a/images/t_default/89a9429b-c0d9-4699-b897-c261fddaf221/W+AIR+MAX+270.png",
          name: "Nike Air Max 270 Women's Shoes",
          price: 6250000,
          unit: "pcs",
          qty: 1,
          desc: "",
        },
        {
          id: 3,
          image_url:
            "https://static.nike.com/a/images/t_default/efcb2629-34c7-484a-9a76-86959a52b272/W+NIKE+ZOOMX+VAPORFLY+NEXT%25+3.png",
          name: "Nike Vaporfly 3 Women's Road Racing Shoes",
          price: 6250000,
          unit: "pcs",
          qty: 1,
          desc: "",
        },
      ],

      arrPaymentMethods: [
        {
          id: 1,
          name: "Bayar Dikasir",
          image_url:
            "https://www.transparentpng.com/thumb/money/kxYuge-money-png-nowskills-apprenticeships.png",
        },
        {
          id: 2,
          name: "QRIS",
          image_url:
            "https://developers.bri.co.id/sites/default/files/2023-02/qris-mpm-dinamis.png",
        },
        {
          id: 3,
          name: "Gopay",
          image_url:
            "https://play-lh.googleusercontent.com/bDgy8Qctp4bT41ioDZvrdOOVYvqscOwoKfBYFLUolNr9m7r0JiSXfzfPRtbZCZ-cWRw",
        },
        {
          id: 4,
          name: "ShopeePay",
          image_url:
            "https://i.pinimg.com/736x/d0/19/16/d019163d861908ed0046391ebfa42ce1.jpg",
        },
        {
          id: 5,
          name: "Dana",
          image_url:
            "https://blogger.googleusercontent.com/img/a/AVvXsEj10i8Btb-dl9eXZNX3Wyn2g_fM28L9bHQOJ37eW7eHhVqNhYdndDf40Ba4Zm88GyKR1bhvbtWmF-fY61RbMnHhP-ea9wJbrZYlg1BLD5xqToO4KE85LbfHbZf6J7R8MdkKcICrdyr8WpBFGw_ujAM-zcmWy8ssLC4Sv8C1nzrL1jtrsNLzyqm6Rr4T=s16000",
        },
        {
          id: 6,
          name: "Transfer Bank",
          image_url:
            "https://img.pikbest.com/png-images/********/payment-bank-transfer-icon-illustration_10612469.png!sw800",
        },
        {
          id: 7,
          name: "Payment Link",
          image_url:
            "https://cdn2.iconfinder.com/data/icons/untact-brepigy/128/ic_cards-512.png",
        },
        {
          id: 8,
          name: "Lainnya",
          image_url:
            "https://static.vecteezy.com/system/resources/thumbnails/009/315/289/small/3d-credit-card-money-financial-security-for-online-shopping-online-payment-credit-card-with-payment-protection-concept-3d-render-for-business-finance-shopping-with-mobile-security-concept-free-png.png",
        },
      ],

      inputs: {
        nominal_transaction: ********,
        payment_type_id: 1,
        payment_type_name: "Tunai",
        payment_mdr: 0,
        payment_poin_redeem: 0,
        payment_total: ********, // nominal_transaction + payment_mdr
        payment_nominal_input: 0, // Nilai yang dibayar
        payment_nominal_return: 0, // Kembalian dari nilai yang dibayar
      },
      errors: {},
    };
  }
  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onTextInputListeners = (text, input) => {
    let inputs = this.state.inputs;
    inputs[input] = text;
    this.setState((prevState) => ({ ...prevState, inputs }));
  };
  onTextErrorListeners = (error, input) => {
    let errors = this.state.errors;
    errors[input] = error;
    this.setState((prevState) => ({ ...prevState, errors }));
  };
  onCalculateGrandTotal = () => {
    let inputs = this.state.inputs;
    inputs.payment_total =
      Number(inputs.nominal_transaction) +
      Number(inputs.payment_mdr) -
      Number(inputs.payment_poin_redeem);
    this.setState({ inputs });
  };
  onValidateListeners = () => {
    this.ref_Loading.onShowDialog();
    setTimeout(() => {
      this.ref_Loading.onCloseDialog();
      this.ref_paymentForm.onShowDialog("kiosk", null);
    }, 500);
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    return (
      <div className={styles.kiosk_payment}>
        <div className={styles.catalogs}>
          <div className={styles.topbar}>
            <div className={styles.store}>
              <a
                onClick={() => {
                  Router.replace({
                    pathname: "/kiosk/pos",
                    query: { from: "payment" },
                  });
                }}
              >
                <i className="ph ph-arrow-left"></i>
              </a>
              <div className={styles.info}>
                <div className={styles.title}>PEMBAYARAN</div>
                <div className={styles.subtitle}>
                  Lengkapi informasi pembayaran Anda
                </div>
              </div>
              <div className={styles.date}>
                <div className={styles.merchant}>YOGYAKARTA STORE #1</div>
                <TimePicker />
              </div>
            </div>
          </div>
          <div className={styles.carts}>
            {this.state.arrCart.length > 0 && (
              <table>
                <thead>
                  <tr>
                    <th>Produk ({this.state.arrCart.length} items)</th>
                    <th className="text-center">Qty</th>
                    <th className="text-right">Harga</th>
                    <th className="text-right">Diskon</th>
                    <th className="text-right">Total</th>
                  </tr>
                </thead>
                <tbody>{this.renderCartItems()}</tbody>
              </table>
            )}
          </div>
          {this.renderCartSummary()}
        </div>
        <div className={styles.payments}>
          {this.renderCustomerDetail()}
          {this.renderPaymentMethods()}
          {this.renderPaymentSummary()}
          {this.renderPaymentActions()}
        </div>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />

        {/* Payment dialog */}
        <TransactionSalesorderCashierPayment
          ref={(value) => (this.ref_paymentForm = value)}
        />
      </div>
    );
  }
  renderCartItems() {
    return this.state.arrCart.map((item, index) => {
      return (
        <tr key={index}>
          <td width="100%">
            <div className={styles.rows}>
              <Tooltip title="Hapus Item Ini">
                <IconButton onClick={() => {}} style={{ marginLeft: -10 }}>
                  <i
                    className="ph ph-bold ph-x-circle"
                    style={{ color: "#e74c3c" }}
                  ></i>
                </IconButton>
              </Tooltip>
              <img
                alt={item.name}
                src={item.image_url || Constants.image_default.empty}
              />
              <div>
                {item.name}
                <span>
                  <i class="ph ph-pencil-simple-line"></i>
                  <input placeholder="Tambahkan catatan disini, jika ada ........." />
                </span>
              </div>
            </div>
          </td>
          <td className="text-center">
            <div className={styles.qty_price}>
              <button className="button danger">
                <i class="ph ph-bold ph-minus"></i>
              </button>
              <input value={item.qty} />
              <button className="button success">
                <i class="ph ph-bold ph-plus"></i>
              </button>
            </div>
          </td>
          <td className="text-right">
            {CommonHelper.formatNumber(item.price, "idr")}
          </td>
          <td className="text-right">Rp0</td>
          <td className="text-right">
            {CommonHelper.formatNumber(item.price, "idr")}
          </td>
        </tr>
      );
    });
  }
  renderCartSummary() {
    return (
      <div className={styles.summaries}>
        <div className={styles.left}>
          <div className={styles.item}>
            <div>Subtotal</div>
            <div>Rp25.000.000</div>
          </div>
          <div className={styles.item}>
            <div className="text-green">Total Diskon</div>
            <div className="text-green">(Rp0)</div>
          </div>
          <div className={styles.item}>
            <div className="text-orange">Pajak (11%)</div>
            <div className="text-orange">Rp2.750.000</div>
          </div>
          <div className={styles.item}>
            <div className="">Pembulatan</div>
            <div className="">Rp0</div>
          </div>
        </div>
        <div className={styles.right}>
          <div style={{ fontSize: "20px", fontWeight: "600" }}>
            Total Transaksi
          </div>
          <div style={{ fontSize: "28px", fontWeight: "700", marginTop: -10 }}>
            Rp27.750.000
          </div>
        </div>
      </div>
    );
  }
  renderCustomerDetail() {
    return (
      <div className={styles.customers}>
        <div className={styles.detail}>
          <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQGXSeTomhrdIan6G5XRR_rk8zJ12BjlmXAG__1AnicoI7PC5HtZe9s26QERd2FqObM6sw&usqp=CAU" />
          <div className={styles.info}>
            <div className={styles.name}>Valentine Doe</div>
            <div className={styles.badge}>Premuim Memberships</div>
          </div>
          <div className={styles.poin}>
            <div className={styles.name}>Poin Tersedia</div>
            <div className={styles.badge}>15.000</div>
          </div>
        </div>
      </div>
    );
  }
  renderPaymentMethods() {
    let arrData = this.state.arrPaymentMethods;
    if (arrData.length > 0) {
      return (
        <div className={styles.methods}>
          <div className={styles.items}>
            {arrData.map((item, index) => {
              return (
                <div
                  key={index}
                  className={`${styles.item} ${
                    this.state.inputs.payment_type_id === item.id &&
                    styles.active
                  }`}
                  onClick={() => {
                    this.onTextInputListeners(item.id, "payment_type_id");
                    this.onTextInputListeners(item.name, "payment_type_name");
                  }}
                >
                  <img
                    alt={item.name}
                    src={item.image_url || Constants.image_default.empty}
                  />
                  <div className={styles.title}>{item.name}</div>
                </div>
              );
            })}
          </div>
        </div>
      );
    }
  }
  renderPaymentSummary() {
    return (
      <div className={styles.summaries}>
        <div className={styles.nominals}>
          <label>Total Transaksi</label>
          <input
            disabled
            value={CommonHelper.formatNumber(
              this.state.inputs.nominal_transaction,
              "idr"
            )}
          />
        </div>
        {this.state.inputs.payment_type_id !== 1 && (
          <div className={styles.nominals}>
            <label className="text-orange">MDR</label>
            <input
              className="text-orange"
              disabled
              value={CommonHelper.formatNumber(
                this.state.inputs.payment_mdr,
                "idr"
              )}
            />
          </div>
        )}
        <div className={styles.nominals}>
          <label className="text-green">POIN REDEEM</label>
          <input
            placeholder="0"
            onChange={(e) => {
              if (e.target !== undefined && e.target.value !== undefined) {
                this.onTextInputListeners(
                  Number(e.target.value),
                  "payment_poin_redeem"
                );
                setTimeout(() => {
                  this.onCalculateGrandTotal();
                }, 100);
              }
            }}
            onBlur={(e) => {
              if (e.target.value === "") {
                this.onTextInputListeners(0, "payment_poin_redeem");
                setTimeout(() => {
                  this.onCalculateGrandTotal();
                }, 100);
              }
            }}
          />
        </div>
        <div className={styles.nominals}>
          <label style={{ color: "rgba(229, 0, 64, 1)" }}>Total Bayar</label>
          <input
            style={{ color: "rgba(229, 0, 64, 1)" }}
            disabled
            value={CommonHelper.formatNumber(
              this.state.inputs.payment_total,
              "idr"
            )}
          />
        </div>
        <div className={styles.nominals}>
          <label>Est. Poin Didapat</label>
          <input disabled value={`${CommonHelper.formatNumber(27750)} Poin`} />
        </div>
      </div>
    );
  }
  renderPaymentActions() {
    return (
      <div className={styles.payment_action}>
        <button
          className="button"
          onClick={() => {
            this.onValidateListeners();
          }}
        >
          <i class="ph ph-bold ph-check-circle"></i>
          <span>Proses Transaksi</span>
        </button>
        <button className="button" style={{ backgroundColor: "#e74c3c" }}>
          <i class="ph ph-bold ph-x-circle"></i>
          <span style={{ marginLeft: "1rem" }}>Batalkan</span>
        </button>
      </div>
    );
  }
}
