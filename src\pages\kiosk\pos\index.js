/*
Created by esoda
Created on Feb, 2025
Contact esoda.id
*/

import React, { useEffect, useRef } from "react";
import Tooltip from "@mui/material/Tooltip";
import CommonHelper from "@/utils/CommonHelper";
import Constants from "@/utils/Constants";
import styles from "@/styles/Kiosk.module.css";
import TimePicker from "@/components/TimePicker";
import Loading from "@/components/modal/Loading";
import CustumerCheckin from "@/components/pages/kiosk/pos/kioskCheckin";
import Router from "next/router";
import useTrackedKioskPosStore from "@/store/kiosk/pos/store";
import AuthWrapper from "@/components/wrapper/AuthWrapper";

const KioskPosPage = ({ isAuthLoading, isLoggedIn }) => {
  const {
    isReady,
    arrCategory,
    arrProducts,
    objCustomer,
    setState,
    onShowCheckIn,
  } = useTrackedKioskPosStore();


  useEffect(() => {
    if (!isAuthLoading && isLoggedIn) {
      setState({
        ref_Loading,
        isReady: false,
        arrCategory: [
          {
            id: 1,
            image_url: "",
            name: "Semua Kategori",
            counts: 1024,
            items: [],
            selected_bool: false,
          },
          {
            id: 2,
            image_url: "",
            name: "Handphone & Tablet",
            counts: 128,
            items: [
              {
                id: 1,
                image_url: "",
                name: "Aksesoris Handphone",
                counts: 1024,
                items: [
                  {
                    id: 1,
                    image_url: "",
                    name: "Charger Mobil",
                    counts: 1024,
                    items: [],
                    selected_bool: false,
                  },
                  {
                    id: 1,
                    image_url: "",
                    name: "Docking Handphone",
                    counts: 1024,
                    items: [],
                    selected_bool: false,
                  },
                  {
                    id: 1,
                    image_url: "",
                    name: "Adaptor Charger",
                    counts: 1024,
                    items: [],
                    selected_bool: false,
                  },
                  {
                    id: 1,
                    image_url: "",
                    name: "Kabel Charger",
                    counts: 1024,
                    items: [],
                    selected_bool: false,
                  },
                ],
                selected_bool: true,
              },
              {
                id: 1,
                image_url: "",
                name: "Handphone",
                counts: 1024,
                items: [],
                selected_bool: false,
              },
              {
                id: 1,
                image_url: "",
                name: "Tablet",
                counts: 1024,
                items: [],
                selected_bool: false,
              },
              {
                id: 1,
                image_url: "",
                name: "Power & Adaptor",
                counts: 1024,
                items: [],
                selected_bool: false,
              },
            ],
            selected_bool: false,
          },
          {
            id: 3,
            image_url: "",
            name: "Komputer",
            counts: 256,
            items: [
              {
                id: 1,
                image_url: "",
                name: "Desktop & Mini PC",
                counts: 39,
                items: [
                  {
                    id: 1,
                    image_url: "",
                    name: "All In One PC",
                    counts: 30,
                    items: [],
                    selected_bool: true,
                  },
                  {
                    id: 1,
                    image_url: "",
                    name: "Komputer Rakitan",
                    counts: 0,
                    items: [],
                    selected_bool: false,
                  },
                  {
                    id: 1,
                    image_url: "",
                    name: "Mini PC",
                    counts: 3,
                    items: [],
                    selected_bool: false,
                  },
                  {
                    id: 1,
                    image_url: "",
                    name: "PC Server",
                    counts: 1,
                    items: [],
                    selected_bool: false,
                  },
                  {
                    id: 1,
                    image_url: "",
                    name: "PC Tower",
                    counts: 5,
                    items: [],
                    selected_bool: false,
                  },
                ],
                selected_bool: true,
              },
              {
                id: 1,
                image_url: "",
                name: "Aksesoris Komputer",
                counts: 1024,
                items: [],
                selected_bool: false,
              },
              {
                id: 1,
                image_url: "",
                name: "Aksesoris PC Gaming",
                counts: 1024,
                items: [],
                selected_bool: false,
              },
              {
                id: 1,
                image_url: "",
                name: "Kabel & Adaptor",
                counts: 1024,
                items: [],
                selected_bool: false,
              },
              {
                id: 1,
                image_url: "",
                name: "Komponen Komputer",
                counts: 1024,
                items: [],
                selected_bool: false,
              },
              {
                id: 1,
                image_url: "",
                name: "Media Penyimpanan Data",
                counts: 1024,
                items: [],
                selected_bool: false,
              },
            ],
            selected_bool: true,
          },
          {
            id: 4,
            image_url: "",
            name: "Laptop",
            counts: 378,
            items: [],
            selected_bool: false,
          },
          {
            id: 5,
            image_url: "",
            name: "Microphone & Sound Card",
            counts: 2,
            items: [],
            selected_bool: false,
          },
          {
            id: 6,
            image_url: "",
            name: "Networking",
            counts: 2,
            items: [],
            selected_bool: false,
          },
          {
            id: 7,
            image_url: "",
            name: "Power Bank",
            counts: 29,
            items: [],
            selected_bool: false,
          },
          {
            id: 8,
            image_url: "",
            name: "Printer",
            counts: 85,
            items: [],
            selected_bool: false,
          },
          {
            id: 9,
            image_url: "",
            name: "Projector & ACC",
            counts: 0,
            items: [],
            selected_bool: false,
          },
          {
            id: 10,
            image_url: "",
            name: "Smart TV",
            counts: 0,
            items: [],
            selected_bool: false,
          },
          {
            id: 11,
            image_url: "",
            name: "Smart Watch",
            counts: 0,
            items: [],
            selected_bool: false,
          },
          {
            id: 12,
            image_url: "",
            name: "Speaker & Audio",
            counts: 123,
            items: [],
            selected_bool: false,
          },
          {
            id: 13,
            image_url: "",
            name: "Webcam & CCTV",
            counts: 0,
            items: [],
            selected_bool: false,
          },
          {
            id: 14,
            image_url: "",
            name: "Uncategorized",
            counts: 0,
            items: [],
            selected_bool: false,
          },
        ],
        arrProducts: [
          {
            id: 1,
            image_url:
              "https://els.id/wp-content/uploads/2024/07/Advan-One-N100-PC.png",
            name: "Advan All in One PC AIO OnePC Intel N100 4GB SSD 128GB",
            price: 4299000,
            unit: "pcs",
            categories: ["Komputer", "Desktop & Mini PC", "All in One PC"],
          },
          {
            id: 2,
            image_url:
              "https://els.id/wp-content/uploads/2024/11/Advan-One-R5.png",
            name: "Advan Force One PC Ryzen 5 6600H 8GB SSD 512GB 24″ FHD W11",
            price: 6299000,
            unit: "pcs",
            categories: ["Komputer", "Desktop & Mini PC", "All in One PC"],
          },
          {
            id: 3,
            image_url:
              "https://els.id/wp-content/uploads/2025/02/All-In-One-Axioo-Hype-2.png",
            name: "All In One Axioo Hype Flex 5 Intel i5-1235U 8GB SSD 256GB/512GB 24″ FHD W11",
            price: 6999000,
            unit: "pcs",
            categories: ["Komputer", "Desktop & Mini PC", "All in One PC"],
          },
          {
            id: 4,
            image_url:
              "https://els.id/wp-content/uploads/2025/03/RTX5080-16GB-GDDR7-MSI-Ventus-3X-OC-3-700x560.png",
            name: "VGA GeForce RTX 5080 16GB Ventus 3X OC GDDR7",
            price: 23499000,
            unit: "pcs",
            categories: ["Komputer", "Komponen Komputer", "VGA Card"],
          },
          {
            id: 5,
            image_url:
              "https://els.id/wp-content/uploads/2024/01/MSI-MAG-Pano-PZ-M100R.png",
            name: "Casing PC MSI MAG PANO M100R PZ",
            price: 1600000,
            unit: "pcs",
            categories: ["Komputer", "Komponen Komputer", "Casing Komputer"],
          },
          {
            id: 6,
            image_url:
              "https://els.id/wp-content/uploads/2024/01/MSI-B760M-Project-Zero-Wifi-4.png",
            name: "Motherboard MSI B760M Project Zero WiFi",
            price: 3180000,
            unit: "pcs",
            categories: ["Komputer", "Komponen Komputer", "Motherboard"],
          },
          {
            id: 7,
            image_url:
              "https://els.id/wp-content/uploads/2024/08/Adata-XPG-Lancer-White-KIT-RGB-2.png",
            name: "RAM PC Adata XPG Lancer White DDR5 RGB 6000MHz KIT",
            price: 1500000,
            unit: "pcs",
            categories: ["Komputer", "Komponen Komputer", "RAM Komputer"],
          },
          {
            id: 8,
            image_url:
              "https://els.id/wp-content/uploads/2025/01/Huawei-MatePad-12X.png",
            name: "Huawei MatePad 12X (12GB/256GB) Kirin T90A 12″ 2,8K PaperMatte Display",
            price: 8700000,
            unit: "pcs",
            categories: ["Handphone & Tablet", "Tablet", "Tablet Android"],
          },
          {
            id: 9,
            image_url:
              "https://els.id/wp-content/uploads/2025/02/Acer-Swift-Lite-14-Air-Edition-SFL14-54M-4.png",
            name: "Acer Swift Lite 14 Air Edition SFL14-54M-56UB – Pure Silver [Core Ultra 5 115U-16GB-SSD 512GB]",
            price: 12500000,
            unit: "pcs",
            categories: ["Laptop", "By Brand", "Acer"],
          },
        ],
        objCustomer: null,
      });
    }
  }, [isAuthLoading, isLoggedIn]);

  const ref_Loading = useRef(null);

  const onInitializeListeners = () => {
    setState({ isReady: true });
  };

  const onSelectedCategoryListeners = (
    parIdx,
    chiIdx = null,
    grdIdx = null
  ) => {
    let updatedCategories = [...arrCategory];
    updatedCategories.map((pItm, pIdx) => {
      pItm.selected_bool = false;
      if (pIdx === parIdx) {
        pItm.selected_bool = true;
      }
      if (chiIdx !== null) {
        pItm.items.map((cItm, cIdx) => {
          cItm.selected_bool = false;
          if (cIdx === chiIdx) {
            cItm.selected_bool = true;
          }
          if (grdIdx !== null) {
            cItm.items.map((gItm, gIdx) => {
              gItm.selected_bool = false;
              if (gIdx === grdIdx) {
                gItm.selected_bool = true;
              }
            });
          }
        });
      }
    });
    setState({ arrCategory: updatedCategories });
  };

  const renderCategories = () => {
    let arrData = arrCategory;
    if (arrData.length > 0) {
      return arrData.map((item, index) => {
        return (
          <>
            <div
              key={index}
              className={`${styles.item} ${
                item.selected_bool && styles.active
              }`}
              onClick={() => onSelectedCategoryListeners(index)}
            >
              <div className={styles.title}>
                <div className={styles.name}>
                  <div>{item.name}</div>
                  <div>
                    {item.counts > 0
                      ? `${CommonHelper.formatNumber(item.counts)} Produk`
                      : "Tidak Ada Produk"}
                  </div>
                </div>
                {item.items.length > 0 && (
                  <i
                    className={`ph ph-bold ${
                      item.selected_bool ? "ph-caret-up" : "ph-caret-down"
                    }`}
                  ></i>
                )}
              </div>
            </div>
            {item.selected_bool &&
              item.items.length > 0 &&
              item.items.map((cItem, cIndex) => {
                return (
                  <>
                    <div
                      key={cIndex}
                      className={`${styles.subs} ${
                        cItem.selected_bool && styles.active
                      }`}
                      onClick={() => onSelectedCategoryListeners(index, cIndex)}
                    >
                      <div className={styles.title}>
                        <div className={styles.name}>
                          <div>{cItem.name}</div>
                          <div>
                            {cItem.counts > 0
                              ? `${CommonHelper.formatNumber(
                                  cItem.counts
                                )} Produk`
                              : "Tidak Ada Produk"}
                          </div>
                        </div>
                        {cItem.items.length > 0 && (
                          <i
                            className={`ph ph-bold ${
                              cItem.selected_bool
                                ? "ph-caret-up"
                                : "ph-caret-down"
                            }`}
                          ></i>
                        )}
                      </div>
                    </div>
                    {cItem.selected_bool &&
                      cItem.items.length > 0 &&
                      cItem.items.map((gItem, gIndex) => {
                        return (
                          <>
                            <div
                              key={gIndex}
                              className={`${styles.menus} ${
                                gItem.selected_bool && styles.active
                              }`}
                              onClick={() =>
                                onSelectedCategoryListeners(
                                  index,
                                  cIndex,
                                  gIndex
                                )
                              }
                            >
                              <div className={styles.title}>
                                <div className={styles.name}>
                                  <div>{gItem.name}</div>
                                  <div>
                                    {gItem.counts > 0
                                      ? `${CommonHelper.formatNumber(
                                          gItem.counts
                                        )} Produk`
                                      : "Tidak Ada Produk"}
                                  </div>
                                </div>
                                {gItem.items.length > 0 && (
                                  <i
                                    className={`ph ph-bold ${
                                      gItem.selected_bool
                                        ? "ph-caret-up"
                                        : "ph-caret-down"
                                    }`}
                                  ></i>
                                )}
                              </div>
                            </div>
                          </>
                        );
                      })}
                  </>
                );
              })}
          </>
        );
      });
    }
  };

  const renderProducts = () => {
    return arrProducts.map((item, index) => {
      return (
        <div
          key={index}
          className={`${styles.item} ${index === 0 && styles.active} ${
            index === 2 && styles.disabled
          }`}
        >
          <img
            alt={item.name}
            src={item.image_url || Constants.image_default.empty}
          />
          <div className={styles.tags}>
            {item.categories.map((cat, key) => {
              return <div key={key}>{cat}</div>;
            })}
          </div>
          <div className={styles.title}>{item.name}</div>
          <div className={styles.price}>
            <div>{CommonHelper.formatNumber(item.price, "idr")}</div>
            <div>Others Info</div>
          </div>
          <button>
            <i
              className="ph ph-bold ph-shopping-cart-simple"
              style={{ marginRight: "0.5rem" }}
            ></i>
            {index === 2 ? "TIDAK TERSEDIA" : "TAMBAHKAN"}
          </button>
        </div>
      );
    });
  };

  return (
    <>
      {!isReady && (
        <div className={styles.loading}>
          <img
            className={styles.overlay}
            src="https://media.screenpal.com/blog/wp-content/uploads/2020/01/13070818/animated-movies.gif"
          />
          <div className={styles.top}>
            <img src="https://els.id/wp-content/uploads/2023/08/ELS-ID-oren.png" />
          </div>
          <div className={styles.center}>
            <h3>Kiosk</h3>
            <h2>Self Service Station</h2>
            <p>
              Maximize your workday with our professional kiosk self service
              station. We believe that the word creative has a positive effect
              on the world. With creativity, a lot of goodness can be created
              for our business.
            </p>

            {/* isAuthLoading & !isLoggedIn */}
            {isAuthLoading && !isLoggedIn && (
              <button className="button" disabled>
                <i className="ph ph-bold ph-shopping-cart-simple"></i>
                <span>memuat data...</span>
              </button>
            )}
            
            
            {/* !isAuthLoading & isLoggedIn */}
            <button className="button" onClick={() => onInitializeListeners()}>
              <i className="ph ph-bold ph-shopping-cart-simple"></i>
              <span>MULAI BELANJA</span>
            </button>
          </div>
          <div className={styles.bottom}>
            <h4>
              Copyright &#169; 2025. Era Solusi Data. All Rights Reserved.
            </h4>
          </div>
        </div>
      )}
      {isReady && (
        <div className={styles.kiosk}>
          <div className={styles.categories}>
            <div className={styles.logo}>
              <img src="https://els.id/wp-content/uploads/2023/08/ELS-ID-oren.png" />
            </div>
            {renderCategories()}
          </div>
          <div className={styles.contents}>
            <div className={styles.topbar}>
              <div className={styles.store}>
                <div className={styles.info}>
                  <div className={styles.name}>YOGYAKARTA STORE #1</div>
                  <div className={styles.address}>
                    Kabupaten Bantul, Daerah Istimewa Yogyakarta
                  </div>
                </div>
                <div className={styles.date}>
                  <div className={styles.cashier}>Selamat Datang</div>
                  <TimePicker />
                </div>
              </div>
              <div className={styles.searchbar}>
                <div className={styles.input_search}>
                  <label htmlFor="search-input">
                    <i className="ph ph-bold ph-magnifying-glass"></i>
                  </label>
                  <input
                    type="text"
                    name="search"
                    id="search-input"
                    placeholder="Tuliskan produk yang ingin Anda cari..."
                  />
                </div>
                <Tooltip title="Filter Data">
                  <a
                    style={{ backgroundColor: "#e67e22" }}
                    onClick={() => {
                      alert("Open Modal Filter Data");
                    }}
                  >
                    <i className="ph ph-bold ph-funnel"></i>
                  </a>
                </Tooltip>
              </div>
              <div className={styles.search_results}>
                1.204 <span>Produk ditemukan</span>
              </div>
            </div>
            <div className={styles.products}>{renderProducts()}</div>
            <div className={styles.cart}>
              <div className={styles.left}>
                <div className={styles.qty}>
                  <b>3</b> Item Produk
                </div>
                <div className={styles.price}>Rp.25.000.000</div>
              </div>
              <div className={styles.right}>
                <button
                  className="button"
                  onClick={() => {
                    onShowCheckIn();
                  }}
                >
                  <i className="ph ph-bold ph-check-circle"></i>
                  <span>Proses Transaksi</span>
                </button>
                <Tooltip title="Batalkan Transaksi (HAPUS)">
                  <button
                    className="button"
                    style={{ backgroundColor: "#e74c3c" }}
                  >
                    <i className="ph ph-bold ph-x-circle"></i>
                  </button>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading dialog */}
      <Loading ref={ref_Loading} />

      {/* Customer Checkin dialog */}
      <CustumerCheckin />
    </>
  );
};

export default AuthWrapper(KioskPosPage, {
  redirectTo: Constants.webUrl.login,
});
