/*
Created by esoda
Created on Feb, 2025
Contact esoda.id
*/

import React from "react";
import { Modal, Box } from "@mui/material";
import useTrackedCheckInStore from "@/store/kiosk/pos/modal/storeCheckIn";
import styles from "@/styles/Kiosk.module.css";

const KioskCheckIn = ({}) => {
  const {
    showDialog,
    isChecked,
    isVerified,
    inputs,
    errors,
    onTextInputListeners,
    onCloseDialog,
    onCheckListeners,
    onVerifiedListeners,
    onNextListeners,
    setState,
    onSubmit,
  } = useTrackedCheckInStore();

  const renderCustomerType = () => {
    return (
      <>
        <div className={styles.kiosk_checkin}>
          <div
            className={inputs.customer_type_id === 2 && styles.active}
            onClick={() => {
              onTextInputListeners(2, "customer_type_id");
              onTextInputListeners("Umum", "customer_type_name");
            }}
          >
            Umum
          </div>
          <div
            className={inputs.customer_type_id === 1 && styles.active}
            onClick={() => {
              onTextInputListeners(1, "customer_type_id");
              onTextInputListeners("Member", "customer_type_name");
              //   setState checked verified
              setState({
                isChecked: false,
                isVerified: false,
              });

              setTimeout(() => {
                const inputWhatsapp = document.getElementById("input-whatsapp");
                if (inputWhatsapp) {
                  inputWhatsapp.focus();
                }
              }, 250);
            }}
          >
            Member
          </div>
        </div>
        {inputs.customer_type_id === 1 && (
          <div className={styles.kiosk_checkin_input}>
            <div className={styles.input_search}>
              <label htmlFor="input-whatsapp">
                <i className="ph ph-whatsapp-logo"></i>
              </label>
              <input
                type="text"
                name="search"
                id="input-whatsapp"
                placeholder="Tuliskan nomor Whatsapp Anda..."
              />
            </div>
            <a
              style={{ backgroundColor: "#3498db" }}
              onClick={() => {
                onCheckListeners();
              }}
            >
              Cek Data
            </a>
          </div>
        )}
        {inputs.customer_type_id === 1 && (
          <p
            style={{
              fontSize: "14px",
              marginTop: ".5rem",
              textAlign: "justify",
            }}
          >
            Jika nomor WA Anda terdaftar sebagai member maka Anda akan menerima
            kode OTP untuk konfirmasi bahwa benar Anda yang melakukan transaksi.
            Terima kasih.
          </p>
        )}
        {inputs.customer_type_id === 1 && isChecked && !isVerified && (
          <div className={styles.kiosk_checkin_input}>
            <div className={styles.input_search}>
              <label htmlFor="input-otp">
                <i className="ph ph-numpad"></i>
              </label>
              <input
                type="text"
                name="search"
                id="input-otp"
                placeholder="Tuliskan kode OTP..."
              />
            </div>
            <a
              style={{ backgroundColor: "#27ae60" }}
              onClick={() => {
                onVerifiedListeners();
              }}
            >
              Konfirmasi
            </a>
            <a
              style={{ backgroundColor: "#e67e22" }}
              onClick={() => {
                alert("Kirim ulang kode OTP");
              }}
            >
              <i
                className="ph ph-bold ph-arrow-counter-clockwise"
                style={{ fontSize: "1.5rem" }}
              ></i>
            </a>
          </div>
        )}
      </>
    );
  };

  const renderCustomerResults = () => {
    return (
      <div
        className="pos-cashier-payment-results"
        style={{
          marginTop: "1rem",
          borderTop: "solid 1px rgb(237, 237, 237)",
        }}
      >
        <div className="icons">
          <i className="ph ph-bold ph-check"></i>
        </div>
        <h3>Pelanggan Terverifikasi!</h3>
        <h2
          style={{
            fontSize: "1rem",
            textAlign: "center",
          }}
        >
          Silahkan lanjutkan ke proses pembayaran untuk transaksi Anda
        </h2>
        <div className="infos">
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>ID Pelanggan</div>
            <div style={{ fontSize: "1.1rem" }}>MBR00992887372</div>
          </div>
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>Nama Pelanggan</div>
            <div style={{ fontSize: "1.1rem" }}>{inputs.customer_name}</div>
          </div>
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>Email</div>
            <div style={{ fontSize: "1.1rem" }}>
              {inputs.customer_email_address}
            </div>
          </div>
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>Nomor Whatsapp</div>
            <div style={{ fontSize: "1.1rem" }}>
              {inputs.customer_wa_number}
            </div>
          </div>
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>Poin Tersedia</div>
            <div style={{ fontSize: "1.1rem" }}>15.000</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <Modal open={showDialog} onClose={onCloseDialog}>
        <Box
          sx={{
            flex: 1,
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            display: "flex",
            width: "100wh",
            height: "100vh",
          }}
        >
          <div className={`modal-content dialog sm`}>
            <div className="modal-header">
              <div className="title">Informasi Pelanggan</div>
              <span className="close" onClick={onCloseDialog}>
                &times;
              </span>
            </div>
            <div className="modal-body">
              {renderCustomerType()}
              {inputs.customer_type_id === 1 &&
                isChecked &&
                isVerified &&
                renderCustomerResults()}
            </div>
            {inputs.customer_type_id === 1 && isChecked && isVerified && (
              <div className="modal-footer">
                <button
                  className="button"
                  onClick={() => {
                    onSubmit();
                  }}
                >
                  <i className="ph ph-bold ph-check-circle"></i>
                  <span>Lanjut Ke Pembayaran</span>
                </button>
                <button className="button cancel" onClick={onCloseDialog}>
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>Batal</span>
                </button>
              </div>
            )}
            {inputs.customer_type_id === 2 && (
              <div className="modal-footer">
                <button
                  className="button danger"
                  onClick={() => {
                    onSubmit();
                  }}
                >
                  <i className="ph ph-bold ph-check-circle"></i>
                  <span>Lanjut Ke Pembayaran</span>
                </button>
                <button className="button cancel" onClick={onCloseDialog}>
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>Batal</span>
                </button>
              </div>
            )}
          </div>
        </Box>
      </Modal>
    </>
  );
};

export default KioskCheckIn;
